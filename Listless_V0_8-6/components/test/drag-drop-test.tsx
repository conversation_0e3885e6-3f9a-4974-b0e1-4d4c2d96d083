"use client"

import { useEffect, useState } from "react"
import { useDragContext } from "../task/drag-context"

/**
 * Test component to validate drag-and-drop functionality
 * This component logs drag events and provides visual feedback for testing
 */
export function DragDropTest() {
  const { isDraggingTask, activeDroppableId, isDraggingOver } = useDragContext()
  const [dragEvents, setDragEvents] = useState<string[]>([])

  useEffect(() => {
    if (isDraggingTask) {
      setDragEvents(prev => [...prev, `🎯 Drag started at ${new Date().toLocaleTimeString()}`])
    } else {
      setDragEvents(prev => [...prev, `🏁 Drag ended at ${new Date().toLocaleTimeString()}`])
    }
  }, [isDraggingTask])

  useEffect(() => {
    if (activeDroppableId) {
      setDragEvents(prev => [...prev, `📍 Hovering over: ${activeDroppableId}`])
    }
  }, [activeDroppableId])

  // Clear events after 10 seconds
  useEffect(() => {
    if (dragEvents.length > 0) {
      const timer = setTimeout(() => {
        setDragEvents([])
      }, 10000)
      return () => clearTimeout(timer)
    }
  }, [dragEvents])

  if (!isDraggingTask && dragEvents.length === 0) {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-sm z-50">
      <h3 className="font-semibold text-sm mb-2">🧪 Drag & Drop Test</h3>
      <div className="space-y-1">
        <div className="text-xs">
          <span className="font-medium">Status:</span> {isDraggingTask ? "🎯 Dragging" : "🏁 Idle"}
        </div>
        {activeDroppableId && (
          <div className="text-xs">
            <span className="font-medium">Target:</span> {activeDroppableId}
          </div>
        )}
        <div className="text-xs">
          <span className="font-medium">Sidebar Tests:</span>
          <ul className="ml-2 mt-1">
            <li>📥 Inbox: {isDraggingOver("inbox") ? "✅" : "⭕"}</li>
            <li>📅 Today: {isDraggingOver("today") ? "✅" : "⭕"}</li>
            <li>⏰ Scheduled: {isDraggingOver("scheduled") ? "✅" : "⭕"}</li>
            <li>⏸️ Deferred: {isDraggingOver("deferred") ? "✅" : "⭕"}</li>
          </ul>
        </div>
      </div>
      {dragEvents.length > 0 && (
        <div className="mt-3 border-t pt-2">
          <div className="text-xs font-medium mb-1">Recent Events:</div>
          <div className="text-xs space-y-1 max-h-20 overflow-y-auto">
            {dragEvents.slice(-5).map((event, index) => (
              <div key={index} className="text-gray-600">{event}</div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
