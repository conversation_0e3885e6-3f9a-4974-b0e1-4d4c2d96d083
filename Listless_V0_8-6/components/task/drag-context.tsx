"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"
import dynamic from "next/dynamic"
import {
  DndContext,
  type DragEndEvent,
  type DragOverEvent,
  type DragStartEvent,
  PointerSensor,
  KeyboardSensor,
  useSensor,
  useSensors,
  MeasuringStrategy,
  defaultDropAnimationSideEffects,
  closestCenter,
  type UniqueIdentifier,
} from "@dnd-kit/core"
import {
  sortableKeyboardCoordinates,
  SortableContext,
  verticalListSortingStrategy,
  arrayMove,
} from "@dnd-kit/sortable"
import { useTaskContext } from "./task-context"
import { useTaskMovement, useBulkTaskMovement } from "@/hooks/use-task-movement"

type DragContextType = {
  activeTaskId: string | null
  selectedTaskIds: Set<string>
  isDraggingTask: boolean
  activeDroppableId: string | null
  isDraggingOver: (id: string) => boolean
  setSelectedTaskIds: (ids: Set<string>) => void
  isMultiDragging: boolean
  dragType: 'sorting' | 'cross-list' | null
  sortableItems: UniqueIdentifier[]
}

const DragContext = createContext<DragContextType>({
  activeTaskId: null,
  selectedTaskIds: new Set<string>(),
  isDraggingTask: false,
  activeDroppableId: null,
  isDraggingOver: () => false,
  setSelectedTaskIds: () => {},
  isMultiDragging: false,
  dragType: null,
  sortableItems: [],
})

export function useDragContext() {
  return useContext(DragContext)
}

export function DragContextProvider({ children }: { children: ReactNode }) {
  const { moveTasksToList, findTaskList, getTasksForList, activeListId } = useTaskContext()
  const taskMovement = useTaskMovement()
  const bulkTaskMovement = useBulkTaskMovement()
  const [activeTaskId, setActiveTaskId] = useState<string | null>(null)
  const [selectedTaskIds, setSelectedTaskIdsInternal] = useState<Set<string>>(new Set())
  const [activeDroppableId, setActiveDroppableId] = useState<string | null>(null)
  const [droppableAreas, setDroppableAreas] = useState<Record<string, boolean>>({})
  const [isMultiDragging, setIsMultiDragging] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const [dragType, setDragType] = useState<'sorting' | 'cross-list' | null>(null)
  const [sortableItems, setSortableItems] = useState<UniqueIdentifier[]>([])

  // Wrapper function to add debugging to selection state changes
  const setSelectedTaskIds = (ids: Set<string>) => {
    console.log("🎯 Selection state changing:", {
      from: Array.from(selectedTaskIds),
      to: Array.from(ids),
      cleared: ids.size === 0
    })
    setSelectedTaskIdsInternal(ids)
  }

  // Enhanced sensors with better activation constraints to prevent sidebar conflicts
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Increased distance to prevent accidental drags
        tolerance: 5,
        delay: 0,
      },
      // Only activate drag on specific elements to prevent sidebar conflicts
      bypassActivationConstraint({event, activeNode}) {
        // Allow drag only from grip handles or task content areas
        const target = event.target as HTMLElement
        const isGripHandle = target.closest('.grip-handle')
        const isTaskContent = target.closest('.task-item-wrapper')
        const isSidebarElement = target.closest('[data-sidebar]')

        // Prevent drag activation on sidebar elements
        if (isSidebarElement) {
          return false
        }

        // Allow drag from grip handles or task content
        return !!(isGripHandle || isTaskContent)
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  // Set client-side flag to prevent hydration issues
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Cleanup function to ensure no lingering drag classes
  useEffect(() => {
    return () => {
      // Cleanup on unmount
      document.body.classList.remove('dragging-active')
      console.log('🧹 Component unmount cleanup complete')
    }
  }, [])

  // Update sortable items when active list changes
  useEffect(() => {
    if (activeListId) {
      const tasks = getTasksForList(activeListId)
      setSortableItems(tasks.map(task => task.id))
    }
  }, [activeListId, getTasksForList])

  // Reset state when component unmounts
  useEffect(() => {
    return () => {
      setActiveTaskId(null)
      setActiveDroppableId(null)
      setDroppableAreas({})
      setIsMultiDragging(false)
    }
  }, [])

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event
    // Check if we're dragging a task (IDs start with "task-")
    if (typeof active.id === "string" && active.id.startsWith("task-")) {
      setActiveTaskId(active.id)

      // Add class to disable hover effects during drag
      document.body.classList.add('dragging-active')
      console.log('🎯 Drag started - hover effects disabled')

      // Determine drag type based on the active element's data
      const dragData = active.data.current
      const isSortingDrag = dragData?.type === "sortable-task"
      setDragType(isSortingDrag ? 'sorting' : 'cross-list')

      // Check if we're dragging a selected task and there are multiple selections
      const isSelectedTask = selectedTaskIds.has(active.id)
      const isMultipleSelection = selectedTaskIds.size > 1

      setIsMultiDragging(isSelectedTask && isMultipleSelection)

      console.log("Drag started:", {
        taskId: active.id,
        dragType: isSortingDrag ? 'sorting' : 'cross-list',
        isSelected: isSelectedTask,
        multipleSelected: isMultipleSelection,
        isMultiDragging: isSelectedTask && isMultipleSelection,
        selectedTasks: Array.from(selectedTaskIds),
      })
    }
  }

  const handleDragOver = (event: DragOverEvent) => {
    const { over } = event

    // Check if we're over a droppable sidebar item
    if (over && typeof over.id === "string" && over.id.startsWith("sidebar-")) {
      const targetId = over.id.replace("sidebar-", "")
      setActiveDroppableId(targetId)

      // Update droppable areas state for visual feedback
      setDroppableAreas((prev) => ({
        ...prev,
        [over.id]: true,
      }))

      console.log("Dragging over:", over.id)
    } else {
      setActiveDroppableId(null)
    }
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    console.log("Drag ended:", {
      active,
      over,
      isMultiDragging,
      selectedTasks: Array.from(selectedTaskIds),
    })

    // Store current state before resetting
    const currentActiveTaskId = activeTaskId
    const currentDragType = dragType
    const currentIsMultiDragging = isMultiDragging

    try {
      // Reset state immediately to prevent conflicts
      setActiveTaskId(null)
      setActiveDroppableId(null)
      setDroppableAreas({})
      setIsMultiDragging(false)
      setDragType(null)

      // Remove dragging class and force hover state cleanup
      document.body.classList.remove('dragging-active')
      console.log('🏁 Drag ended - re-enabling hover effects')

      // Simple hover state cleanup without using hover-reset class
      console.log('🧹 Starting simple hover state cleanup...')
      setTimeout(() => {
        resetHoverStates()
        console.log('✅ Simple hover state cleanup complete')
      }, 50)
    } catch (error) {
      console.error('❌ Error during drag cleanup:', error)
      // Ensure cleanup happens even if there's an error
      document.body.classList.remove('dragging-active')
      console.log('🚨 Emergency cleanup complete')
    }

    // Handle sorting within the same list
    if (currentDragType === 'sorting' && currentActiveTaskId && over && typeof over.id === "string" && over.id.startsWith("task-")) {
      const activeIndex = sortableItems.indexOf(currentActiveTaskId)
      const overIndex = sortableItems.indexOf(over.id)

      if (activeIndex !== overIndex && activeIndex !== -1 && overIndex !== -1) {
        const newItems = arrayMove(sortableItems, activeIndex, overIndex)
        setSortableItems(newItems)

        // TODO: Update task order in the backend
        console.log("Reordered tasks:", { from: activeIndex, to: overIndex, newOrder: newItems })
      }
    }
    // If we have an active task and it's dropped on a sidebar item (cross-list drag)
    if (currentActiveTaskId && over && typeof over.id === "string" && over.id.startsWith("sidebar-")) {
      const targetView = over.id.replace("sidebar-", "")
      const sourceListId = findTaskList(currentActiveTaskId)

      console.log("Moving task(s) to:", targetView, "from:", sourceListId)

      // Determine if this is a project drop
      const isProjectDrop = !['inbox', 'today', 'scheduled', 'deferred', 'completed', 'trash'].includes(targetView)
      const projectId = isProjectDrop ? targetView : undefined

      if (currentIsMultiDragging && selectedTaskIds.size > 1) {
        // Move all selected tasks using bulk movement
        const tasksToMove = Array.from(selectedTaskIds).map(taskId => ({
          taskId,
          targetView: isProjectDrop ? 'project' : targetView,
          projectId,
        }))

        console.log("Moving multiple tasks:", tasksToMove)
        try {
          bulkTaskMovement.mutate(tasksToMove, {
            onError: (error) => {
              console.error('❌ Bulk task movement failed:', error)
              // Don't let the error freeze the UI
            },
            onSuccess: () => {
              console.log('✅ Bulk task movement successful')
            }
          })
        } catch (error) {
          console.error('❌ Error initiating bulk task movement:', error)
        }
      } else {
        // Move single task
        console.log("Moving single task:", currentActiveTaskId, "to:", targetView)
        try {
          taskMovement.mutate({
            taskId: currentActiveTaskId,
            targetView: isProjectDrop ? 'project' : targetView,
            projectId,
          }, {
            onError: (error) => {
              console.error('❌ Task movement failed:', error)
              // Don't let the error freeze the UI
            },
            onSuccess: () => {
              console.log('✅ Task movement successful')
            }
          })
        } catch (error) {
          console.error('❌ Error initiating task movement:', error)
        }
      }

      // Clear selection after successful drop
      setSelectedTaskIdsInternal(new Set())
    }
  }

  // Helper function to reset hover states on task items only
  const resetHoverStates = () => {
    try {
      // Force a simple reflow to clear any stuck CSS :hover states
      const taskItems = document.querySelectorAll('.task-list-container .task-item, .task-list-container .task-container')
      console.log(`🔧 Resetting hover states on ${taskItems.length} task items`)

      // Simple approach: just force a reflow without manipulating pointer-events
      taskItems.forEach((element) => {
        const htmlElement = element as HTMLElement
        // Force a reflow by accessing offsetHeight
        htmlElement.offsetHeight
      })

      console.log('✅ Hover state reset complete')
    } catch (error) {
      console.error('❌ Error resetting hover states:', error)
    }
  }

  // Helper function to check if dragging over a specific area
  const isDraggingOver = (id: string): boolean => {
    const fullId = id.startsWith("sidebar-") ? id : `sidebar-${id}`
    return !!droppableAreas[fullId]
  }

  // Custom drop animation to maintain consistent size
  const dropAnimation = {
    sideEffects: defaultDropAnimationSideEffects({
      styles: {
        active: {
          opacity: "0.5",
        },
      },
    }),
  }

  // Don't render DndContext on server to prevent hydration issues
  if (!isClient) {
    return (
      <DragContext.Provider
        value={{
          activeTaskId: null,
          selectedTaskIds: new Set(),
          isDraggingTask: false,
          activeDroppableId: null,
          isDraggingOver: () => false,
          setSelectedTaskIds: () => {},
          isMultiDragging: false,
          dragType: null,
          sortableItems: [],
        }}
      >
        {children}
      </DragContext.Provider>
    )
  }

  return (
    <DragContext.Provider
      value={{
        activeTaskId,
        selectedTaskIds,
        isDraggingTask: activeTaskId !== null,
        activeDroppableId,
        isDraggingOver,
        setSelectedTaskIds,
        isMultiDragging,
        dragType,
        sortableItems,
      }}
    >
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
        collisionDetection={closestCenter}
        measuring={{
          droppable: {
            strategy: MeasuringStrategy.Always, // Always measure to improve accuracy
          },
        }}
        modifiers={[]} // Remove any modifiers that might cause lag
        // @ts-ignore - Type issue with dropAnimation
        dropAnimation={dropAnimation}
      >
        <SortableContext items={sortableItems} strategy={verticalListSortingStrategy}>
          {children}
        </SortableContext>
      </DndContext>
    </DragContext.Provider>
  )
}
